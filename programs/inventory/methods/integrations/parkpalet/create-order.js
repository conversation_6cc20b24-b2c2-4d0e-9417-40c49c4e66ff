import _ from 'lodash';
import {handleIntegrationError, ParkPaletClient} from './utils';

export default async function createOrder(app, integration, payload) {
    try {
        const client = await ParkPaletClient(app, integration.integrationParams);

        if (!payload.documentId) {
            throw new Error('Document ID is required.');
        }
        if (!(Array.isArray(payload.items) && payload.items.length > 0)) {
            throw new Error('No items found! At least one product is required.');
        }

        if (payload.isMarketplaceOrder) {
            let shouldSendToParkPalet = integration.sendIntegrationOrderForMarketplaces;

            if (payload.storeId) {
                const store = await app.collection('ecommerce.stores').findOne({
                    _id: payload.storeId,
                    $select: ['sendMarketplaceOrdersToParkPalet'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (store && store.sendMarketplaceOrdersToParkPalet !== undefined) {
                    shouldSendToParkPalet = store.sendMarketplaceOrdersToParkPalet;
                }
            }

            if (!shouldSendToParkPalet) {
                if (!payload.marketplaceOrderId) {
                    throw new Error('Marketplace order ID not found.');
                }

                await app.collection('inventory.transfers').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: payload.documentId},
                            update: {
                                $set: {
                                    'integrationPayload.orderId': payload.marketplaceOrderId,
                                    'integrationPayload.status': 'processing',
                                    'integrationPayload.orderType': 'external-marketplace-order',
                                    'integrationPayload.integrationId': integration._id,
                                    'integrationPayload.isIntegrationTransfer': true,
                                    'integrationPayload.isMarketplaceOrder': true
                                }
                            }
                        }
                    }
                ]);

                return;
            }
        }

        if (payload.orderType === 'sale-order') {
            const erpProductIds = payload.items.map(item => item.id);
            const integrationProducts = await app.collection('inventory.integration-products').find({
                erpProductId: {$in: erpProductIds},
                integrationId: integration._id
            });
            const erpProducts = await app.collection('inventory.products').find({
                _id: {$in: erpProductIds},
                $select: ['_id', 'barcode', 'code', 'definition'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            const unmatchedItems = payload.items.filter(
                item => !integrationProducts.some(product => product.erpProductId === item.id)
            );
            if (unmatchedItems.length > 0) {
                const unmatchedDetails = unmatchedItems.map(item => `(${item.code} - ${item.name})`).join(', ');

                throw new Error(
                    `We couldn't find matching records for some items in our system. Please review the following products: ${unmatchedDetails}`
                );
            }

            const uniqueWarehouseIds = _.uniq(integrationProducts.map(product => product.integrationWarehouseId).filter(id => id));
            if (uniqueWarehouseIds.length < 1) {
                throw new Error(
                    'No warehouse assigned to the products in your order. Please ensure that all products are linked to a valid warehouse.'
                );
            }
            if (uniqueWarehouseIds.length > 1) {
                throw new Error(
                    'Your order contains products that are stored in different warehouses. Please ensure all products are from the same warehouse.'
                );
            }

            const integrationWarehouse = await app.collection('inventory.integration-warehouses').findOne({
                integrationWarehouseId: uniqueWarehouseIds[0],
                $select: ['integrationWarehouseId', 'integrationCargoCompanyId']
            });
            if (!integrationWarehouse || !integrationWarehouse.integrationWarehouseId) {
                const allIntegrationWarehouses = await app.collection('inventory.integration-warehouses').find({
                    integrationId: integration._id,
                    $select: ['integrationWarehouseId', 'integrationWarehouseName', 'erpWarehouseId']
                });

                try {
                    const warehouseList = await app.rpc('inventory.get-integration-warehouse-list', {
                        integrationId: integration._id
                    });

                    const missingWarehouse = warehouseList.find(w => w.id === uniqueWarehouseIds[0]);
                    if (missingWarehouse && payload.warehouseId) {
                        const newIntegrationWarehouse = await app.collection('inventory.integration-warehouses').create({
                            integrationWarehouseId: missingWarehouse.id,
                            integrationWarehouseName: missingWarehouse.name,
                            erpWarehouseId: payload.warehouseId,
                            integrationId: integration._id,
                            isActive: true
                        });

                        integrationWarehouse = {
                            integrationWarehouseId: newIntegrationWarehouse.integrationWarehouseId,
                            integrationCargoCompanyId: newIntegrationWarehouse.integrationCargoCompanyId
                        };
                    } else {
                        throw new Error('Could not find warehouse in ParkPalet API or missing ERP warehouse ID');
                    }
                } catch (autoCreateError) {
                    throw new Error(
                        `No integration warehouse found with ID '${uniqueWarehouseIds[0]}'. Available integration warehouses: ${allIntegrationWarehouses.map(w => `${w.integrationWarehouseId} (${w.integrationWarehouseName})`).join(', ') || 'None'}. Please configure the integration warehouse mapping in the integration settings.`
                    );
                }
            }

            const partner = await app.collection('kernel.partners').findOne({
                _id: payload.partnerId,
                $select: ['name', 'email', 'isCompany', 'identity', 'taxDepartment', 'phoneNumbers'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            if (!partner) {
                throw new Error('Partner not found.');
            }
            if (!partner.phoneNumbers || !(Array.isArray(partner.phoneNumbers) && partner.phoneNumbers.length > 0)) {
                throw new Error('At least one phone number is required for the partner.');
            }

            const phoneNumber = partner.phoneNumbers[0];
            if (!phoneNumber || !phoneNumber.phoneCode || !phoneNumber.number) {
                throw new Error('The phone number must include both phone code and number.');
            }

            const deliveryAddress = payload.deliveryAddress;
            if (!_.isPlainObject(deliveryAddress)) {
                throw new Error('Delivery address not found.');
            }
            if (!deliveryAddress.postalCode) {
                throw new Error('Delivery address must include postal code.');
            }
            if (!deliveryAddress.address) {
                throw new Error('Delivery address must include street address.');
            }
            if (!deliveryAddress.countryId) {
                throw new Error('Delivery address must include country.');
            }

            const currency = await app.collection('kernel.currencies').findOne({
                _id: payload.currencyId,
                $select: ['name'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });
            if (!currency) {
                throw new Error('No currency found for this transfer.');
            }

            const orderPayload = {};
            orderPayload.expectedShippingDate = payload.scheduledDate || '';
            orderPayload.salesType = 'Retail';
            // orderPayload.cargoFirmName = integrationWarehouse.integrationCargoCompanyId || '';
            // orderPayload.cargoFirmName = '';
            orderPayload.totalPriceAmount = app.roundNumber(payload.total, 2);
            orderPayload.totalPriceCurrency = currency.name === 'TL' ? 'TRY' : currency.name;
            orderPayload.platform = 'Manual';
            orderPayload.departureWarehouseId = integrationWarehouse.integrationWarehouseId;
            orderPayload.items = payload.items.map(item => {
                const integrationProduct = integrationProducts.find(ip => ip.erpProductId === item.id);
                const erpProduct = erpProducts.find(p => p._id === item.id);

                if (!integrationProduct) {
                    throw new Error(
                        `Integration product not found for the following erp product: ${item.code} - ${item.name}`
                    );
                }
                if (!erpProduct) {
                    throw new Error(
                        `Erp product not found for the following transfer item: ${item.code} - ${item.name}`
                    );
                }
                if (integrationProduct.integrationProductBarcode !== erpProduct.barcode) {
                    throw new Error(`Barcode matching failed for the following product: ${item.code} - ${item.name}`);
                }

                return {
                    sku: item.code,
                    name: item.name,
                    priceAmount: app.roundNumber(item.unitPrice, 2),
                    barcode: integrationProduct.integrationProductBarcode,
                    quantity: item.quantity,
                    priceCurrency: currency.name === 'TL' ? 'TRY' : currency.name
                };
            });
            orderPayload.shippingAddress = {
                owner: {
                    title: partner.name,
                    phoneCode: phoneNumber.phoneCode,
                    phoneNumber: phoneNumber.number
                },
                countryCode: (
                    (await app.collection('kernel.countries').findOne({
                        _id: deliveryAddress.countryId,
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    })) || {}
                ).code,
                stateCode: deliveryAddress.state,
                cityName: deliveryAddress.city,
                districtName: deliveryAddress.district,
                postalCode: deliveryAddress.postalCode,
                firstLine: deliveryAddress.address
            };

            const order = (await client.post('/orders/v1', orderPayload)).data;

            if (order && order.orderId) {
                await app.collection('inventory.transfers').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: payload.documentId},
                            update: {
                                $set: {
                                    'integrationPayload.orderId': order.orderId,
                                    'integrationPayload.status': 'processing',
                                    'integrationPayload.orderType': 'sale-order',
                                    'integrationPayload.integrationId': integration._id,
                                    'integrationPayload.isIntegrationTransfer': true,
                                    'integrationPayload.isMarketplaceOrder': payload.isMarketplaceOrder,
                                    'integrationPayload.allowedShippingStatuses': [
                                        'order-created',
                                        'shipment-prepared',
                                        'in-transfer-phase',
                                        'delivered'
                                    ]
                                }
                            }
                        }
                    }
                ]);
            }
        }

        if (payload.orderType === 'purchase-order') {
            const erpProductIds = payload.items.map(item => item.id);
            const erpProducts = await app.collection('inventory.products').find({
                _id: {$in: erpProductIds},
                $select: ['_id', 'barcode'],
                $disableActiveCheck: true,
                $disableSoftDelete: true
            });

            const integrationWarehouse = await app.collection('inventory.integration-warehouses').findOne({
                erpWarehouseId: payload.warehouseId,
                $select: ['integrationWarehouseId']
            });

            if (!integrationWarehouse) {
                throw new Error('Integration warehouse not found.');
            }

            const orderPayload = {};
            orderPayload.warehouseId = integrationWarehouse.integrationWarehouseId;
            orderPayload.departureCountryCode = 'TR';
            orderPayload.transportationServiceType = 'NoTransportation';
            orderPayload.items = payload.items.map(item => {
                const erpProduct = erpProducts.find(p => p._id === item.id);

                if (!erpProduct) {
                    throw new Error('Erp product not found.');
                }
                if (!erpProduct.barcode) {
                    throw new Error(`Barcode not found for the following transfer item: ${item.code} - ${item.name}`);
                }

                return {
                    barcode: erpProduct.barcode,
                    quantity: item.quantity
                };
            });

            const order = (await client.post('/purchaseorders/v1', orderPayload)).data;

            if (order && order.purchaseOrderId) {
                await app.collection('inventory.transfers').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: payload.documentId},
                            update: {
                                $set: {
                                    'integrationPayload.orderId': order.purchaseOrderId,
                                    'integrationPayload.status': 'processing',
                                    'integrationPayload.orderType': 'purchase-order',
                                    'integrationPayload.integrationId': integration._id,
                                    'integrationPayload.isIntegrationTransfer': true
                                }
                            }
                        }
                    }
                ]);
            }
        }
    } catch (error) {
        if (payload.documentId) {
            await app.collection('inventory.transfers').bulkWrite([
                {
                    updateOne: {
                        filter: {_id: payload.documentId},
                        update: {
                            $set: {
                                'integrationPayload.status': 'failed',
                                'integrationPayload.orderType': payload.orderType,
                                'integrationPayload.integrationId': integration._id,
                                'integrationPayload.isIntegrationTransfer': true
                            }
                        }
                    }
                }
            ]);
        }

        handleIntegrationError({
            app,
            error,
            shouldLog: true,
            documentId: integration._id,
            documentIdentifier: integration.code,
            referenceId: payload.documentId
        });
    }
}
