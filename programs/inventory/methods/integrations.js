import parkpalet from './integrations/parkpalet';

const integrations = {
    parkpalet
};

export default [
    {
        name: 'get-integration-warehouse-list',
        async action({integrationId}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(app, integration, 'getWarehouseList');
        }
    },
    {
        name: 'get-integration-products',
        async action({integrationId}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(app, integration, 'getProducts');
        }
    },
    {
        name: 'get-integration-cargo-companies',
        async action({integrationId, warehouseId}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(app, integration, 'getCargoCompanies', warehouseId);
        }
    },
    {
        name: 'create-integration-order',
        async action(payload) {
            const app = this.app;

            const integrationWarehouse = await app.collection('inventory.integration-warehouses').findOne({
                erpWarehouseId: payload.warehouseId,
                $select: ['integrationId']
            });

            if (integrationWarehouse && integrationWarehouse.integrationId) {
                const integration = await getIntegration(app, integrationWarehouse.integrationId);

                return await executeIntegrationMethod(app, integration, 'createOrder', payload);
            }
        }
    },
    {
        name: 'update-integration-transfer',
        async action({integrationId, documentId, orderId, payload}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(
                app,
                integration,
                'updateIntegrationTransfer',
                documentId,
                orderId,
                payload
            );
        }
    },
    {
        name: 'resend-integration-order',
        async action({integrationId, documentId}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(app, integration, 'resendIntegrationOrder', documentId);
        }
    },
    {
        name: 'get-integration-stock-report',
        async action({integrationId}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(app, integration, 'getStockReport');
        }
    },
    {
        name: 'cancel-integration-order',
        async action({integrationId, documentId}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(app, integration, 'cancelOrder', documentId);
        }
    },
    {
        name: 'download-integration-sample-products',
        async action({integrationId}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(app, integration, 'downloadSampleProducts');
        }
    },
    {
        name: 'import-integration-products',
        async action({integrationId, fileId}) {
            const app = this.app;
            const integration = await getIntegration(app, integrationId);

            return await executeIntegrationMethod(app, integration, 'importProducts', fileId);
        }
    }
];

async function getIntegration(app, integrationId) {
    return await app.collection('inventory.integrations').findOne({
        _id: integrationId,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
}

async function executeIntegrationMethod(app, integration, methodName, ...args) {
    const integrationType = integrations[integration?.integrationType];

    if (integrationType && typeof integrationType[methodName] === 'function') {
        return await integrationType[methodName](app, integration, ...args);
    }
}
