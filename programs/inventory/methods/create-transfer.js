import _ from 'lodash';
import Random from 'framework/random';

export default {
    name: 'create-transfer',
    // schema: {
    //     from: {
    //         type: 'string',
    //         allowed: [
    //             'sales-order',
    //             'sales-return',
    //             'customer-invoice',
    //             'customer-return-invoice',
    //             'purchase-order',
    //             'purchase-return',
    //             'vendor-invoice',
    //             'vendor-return-invoice',
    //             'production-order-issues',
    //             'production-order-receipts',
    //             'transport-incoming',
    //             'transport-outgoing',
    //             'partial-transfer',
    //             'expense',
    //             'income'
    //         ]
    //     },
    //     data: {
    //         type: 'object',
    //         blackbox: true,
    //         required: false
    //     }
    // },
    async action(payload, params) {
        const app = this.app;
        const company = await app.collection('kernel.company').findOne({});
        let items = payload.data.items || [];
        const transfers = [];

        // Check data.
        if (!payload.data.warehouseId) throw new Error('Warehouse is required!');
        if (!_.isDate(payload.data.scheduledDate)) throw new Error('Scheduled date is required!');

        // Virtual locations.
        const customerLocationId = (
            await app.collection('inventory.locations').findOne({
                type: 'customer',
                system: true,
                $select: ['_id']
            })
        )._id;
        const vendorLocationId = (
            await app.collection('inventory.locations').findOne({
                type: 'vendor',
                system: true,
                $select: ['_id']
            })
        )._id;
        const productionLocationId = (
            await app.collection('inventory.locations').findOne({
                type: 'production',
                system: true,
                $select: ['_id']
            })
        )._id;
        const transitLocationId = (
            await app.collection('inventory.locations').findOne({
                type: 'transit',
                system: true,
                $select: ['_id']
            })
        )._id;

        // Get journal.
        const journal = await app.collection('accounting.journals').findOne({
            type: 'stock',
            $select: ['_id']
        });

        // Get currencies.
        const currencies = await app.collection('kernel.currencies').find({
            $select: ['name']
        });

        // Get product IDS.
        let productIds = _.uniq(items.map(item => item.productId));

        // Find warehouse operation type.
        let warehouseOperationType = '';
        switch (payload.from) {
            case 'sales-order':
            case 'purchase-return':
            case 'vendor-return-invoice':
            case 'customer-invoice':
            case 'transport-outgoing':
            case 'income':
            case 'production-order-issues':
            case 'service-work-order-outgoing':
                warehouseOperationType = 'outgoing';
                break;
            case 'sales-return':
            case 'customer-return-invoice':
            case 'purchase-order':
            case 'vendor-invoice':
            case 'production-order':
            case 'transport-incoming':
            case 'expense':
            case 'production-order-receipts':
            case 'service-work-order-incoming':
                warehouseOperationType = 'incoming';
                break;
            case 'partial-transfer':
                warehouseOperationType = payload.data.warehouseOperationType;
                break;
            default:
                warehouseOperationType = 'internal';
                break;
        }

        // Find and replace kit products.
        let kitProducts = await app.collection('inventory.products').find({
            _id: {$in: productIds},
            isKit: true,
            $select: ['isKit', 'subProducts'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        const kitProductsMap = {};
        for (const kitProduct of kitProducts) {
            kitProductsMap[kitProduct._id] = kitProduct;
        }
        const kitItems = [];
        const kitProductIds = [];
        for (const item of items) {
            const product = kitProductsMap[item.productId];

            if (_.isPlainObject(product) && product.isKit) {
                const kitSubProducts = await app.collection('inventory.products').find({
                    _id: {
                        $in: product.subProducts.map(item => item.productId)
                    },
                    $select: ['_id', 'code', 'definition', 'barcode'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
                const subProducts = product.subProducts.filter(subProduct => subProduct.type !== 'service');

                const perUnitPrice = _.isNumber(item.unitPrice)
                    ? item.unitPrice / _.sumBy(product.subProducts, 'quantity')
                    : 0;

                for (const subProduct of subProducts) {
                    const kitSubProduct = kitSubProducts.find(item => item._id === subProduct.productId);

                    kitItems.push({
                        productId: kitSubProduct._id,
                        productCode: kitSubProduct.code,
                        productDefinition: kitSubProduct.definition,
                        productType: subProduct.type,
                        barcode: kitSubProduct.barcode ?? '',
                        scheduledDate: item.scheduledDate,
                        warehouseId: item.warehouseId,
                        financialProjectId: item.financialProjectId,
                        deliveryNote: item.deliveryNote,
                        unitId: subProduct.unitId,
                        quantity: subProduct.quantity * item.quantity,
                        ...(_.isNumber(item.unitPrice)
                            ? {
                                  unitPrice: app.round(subProduct.quantity * perUnitPrice, 'unit-price')
                              }
                            : {}),
                        unitPrice: item.unitPrice || 0,
                        deliveryReceiverId: item.deliveryReceiverId,
                        deliveryAddressId: item.deliveryAddressId,
                        deliveryAddress: item.deliveryAddress,
                        ...(app.hasModule('pcm')
                            ? {
                                  pcmModelId: item.pcmModelId,
                                  pcmConfigurationId: item.pcmConfigurationId,
                                  pcmHash: item.pcmHash
                              }
                            : {})
                    });
                }

                kitProductIds.push(product._id);
            }
        }
        items = items
            .concat(kitItems)
            .filter(item => kitProductIds.indexOf(item.productId) === -1 && item.productType !== 'service');

        // Get and normalize items.
        items = items.map(item => {
            if (!_.isDate(item.scheduledDate)) item.scheduledDate = payload.data.scheduledDate;
            if (!_.isString(item.warehouseId)) item.warehouseId = payload.data.warehouseId;
            if (!_.isNumber(item.unitPrice)) item.unitPrice = 0;
            if (!_.isPlainObject(item.deliveryAddress)) item.deliveryAddress = {};
            if (!_.isString(item.deliveryAddress.address)) item.deliveryAddress.address = '';

            if (warehouseOperationType === 'incoming') {
                item.scheduledDate = app.datetime.fromJSDate(item.scheduledDate).startOf('day').toJSDate();
            } else if (warehouseOperationType === 'outgoing') {
                item.scheduledDate = app.datetime.fromJSDate(item.scheduledDate).endOf('day').toJSDate();
            }

            return item;
        });

        if (warehouseOperationType === 'incoming') {
            payload.data.scheduledDate = app.datetime.fromJSDate(payload.data.scheduledDate).startOf('day').toJSDate();
        } else if (warehouseOperationType === 'outgoing') {
            payload.data.scheduledDate = app.datetime.fromJSDate(payload.data.scheduledDate).endOf('day').toJSDate();
        }

        // Get warehouses.
        const warehouseIds = _.uniq(items.map(item => item.warehouseId));
        const warehouses = await app.collection('inventory.warehouses').find({
            _id: {$in: warehouseIds},
            $select: ['shortName', 'name', 'branchId'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        const warehousesMap = {};
        for (const warehouse of warehouses) {
            warehousesMap[warehouse._id] = warehouse;
        }

        // Re-assign product ids due to kit product concatenation
        productIds = _.uniq(items.map(item => item.productId));

        // Get products.
        const products = await app.collection('inventory.products').find({
            _id: {$in: productIds},
            $select: [
                'code',
                'name',
                'definition',
                'displayName',
                'baseUnitId',
                'tracking',
                'unitRatios',
                'putawayStrategy',
                'removalStrategy',
                'warehouseManagement',
                'negativeStock'
            ],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        const productsMap = {};
        for (const product of products) {
            productsMap[product._id] = product;
        }

        // Get stock report.
        const stockQuery = {
            ...(!!payload.data.referenceId
                ? {
                      externalReservationsQuery: {
                          documentId: {$ne: payload.data.referenceId}
                      }
                  }
                : {}),
            date: payload.data.scheduledDate,
            productId: productIds,
            warehouseId: warehouseIds,
            query: {}
        };
        if (app.hasModule('pcm')) {
            const hashes = [];

            for (const item of items) {
                if (!!item.pcmHash) {
                    hashes.push(item.pcmHash);
                }
            }

            if (hashes.length > 0) {
                stockQuery.query.pcmHash = {$in: hashes};
            }
        }
        const report = await app.rpc('inventory.get-stock-report', stockQuery, {user: params.user});

        // Prepare transfers.
        for (const item of items) {
            const existingIndex = _.findIndex(
                transfers,
                transfer =>
                    transfer.warehouseId === item.warehouseId &&
                    transfer.scheduledDate.getTime() === item.scheduledDate.getTime() &&
                    transfer.deliveryAddress.address === item.deliveryAddress.address
            );
            const product = productsMap[item.productId];
            const warehouse = warehousesMap[item.warehouseId];
            const r = report.find(r => r.productId === item.productId && r.warehouseId === item.warehouseId);
            let transfer = {items: []};

            if (existingIndex === -1) {
                const now = app.datetime.local();
                const issueDate = app.datetime
                    .fromJSDate(payload.data.issueDate)
                    .set({
                        hour: now.hour,
                        minute: now.minute,
                        second: 0
                    })
                    .toJSDate();

                // General information.
                transfer.status = 'waiting';
                transfer.branchId = warehouse.branchId;
                transfer.warehouseId = item.warehouseId;
                transfer.scheduledDate = item.scheduledDate;
                transfer.recordDate = payload.data.recordDate;
                transfer.issueDate = issueDate;
                transfer.dueDate = payload.data.dueDate;
                transfer.partnerId = payload.data.partnerId;
                transfer.reference = payload.data.reference;
                transfer.referenceId = payload.data.referenceId;
                transfer.referenceCollection = payload.data.referenceCollection;
                transfer.deliveryReceiverId = item.deliveryReceiverId;
                transfer.deliveryAddressCode = item.deliveryAddressCode || payload.data.deliveryAddressCode;
                transfer.deliveryAddressId = item.deliveryAddressId;
                transfer.deliveryAddress = item.deliveryAddress;
                transfer.deliveryPriority = payload.data.deliveryPriority || 'normal';
                transfer.deliveryPolicy = payload.data.deliveryPolicy || 'when-one-ready';
                transfer.deliveryConditionId = payload.data.deliveryConditionId;
                transfer.deliveryMethodId = payload.data.deliveryMethodId;
                transfer.shippingDocumentType = payload.data.shippingDocumentType || 'none';
                transfer.carrierId = payload.data.carrierId;
                transfer.cargoTrackingCode = payload.data.cargoTrackingCode;
                transfer.journalId = journal._id;
                transfer.invoiceResponsibleId = payload.data.invoiceResponsibleId;
                transfer.invoiceAddressId = payload.data.invoiceAddressId;
                transfer.invoiceAddress = payload.data.invoiceAddress;
                transfer.partnerOrderReference = payload.data.partnerOrderReference;
                transfer.partnerOrderReference = payload.data.partnerOrderReference;
                transfer.partnerOrderDate = payload.data.partnerOrderDate;
                transfer.financialProjectId = payload.data.financialProjectId;
                transfer.connectedTransferId = payload.data.connectedTransferId;
                transfer.relatedDocuments = payload.data.relatedDocuments || [];
                transfer.invoicing = payload.data.invoicing || 'none';
                transfer.currencyId = payload.data.currencyId;
                transfer.currencyRate = payload.data.currencyRate || 1;
                transfer.exchangeRates = payload.data.exchangeRates || [];
                transfer.exchangeRatesMap = payload.data.exchangeRatesMap || {};
                transfer.freight = payload.data.freight;
                transfer.priceSourceId = payload.data.priceSourceId;
                transfer.isLocked = true;

                if (!transfer.currencyId) {
                    transfer.currencyId = company.currencyId;
                    transfer.currencyRate = 1;
                }
                if (!Array.isArray(transfer.exchangeRates) || transfer.exchangeRates.length < 1) {
                    const exchangeRates = [];
                    const payloads = [];

                    for (const currency of currencies) {
                        if (currency.name === company.currency.name) {
                            continue;
                        }

                        payloads.push({
                            from: currency.name,
                            to: company.currency.name,
                            value: 1,
                            options: {
                                date: transfer.issueDate
                            }
                        });
                    }

                    for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
                        exchangeRates.push({
                            currencyName: payload.from,
                            rate: payload.rate
                        });
                    }

                    transfer.exchangeRates = exchangeRates;
                }
                const exchangeRatesMap = {};
                for (const exchangeRate of transfer.exchangeRates || []) {
                    exchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
                }
                transfer.exchangeRatesMap = exchangeRatesMap;

                // Document type.
                if (
                    payload.from === 'sales-return' ||
                    payload.from === 'purchase-return' ||
                    payload.from === 'customer-return-invoice' ||
                    payload.from === 'vendor-return-invoice'
                ) {
                    transfer.documentType = 'return-transfers';
                } else if (payload.from === 'transport-incoming' || payload.from === 'transport-outgoing') {
                    transfer.documentType = 'transport-transfers';
                } else if (payload.from === 'partial-transfer') {
                    transfer.documentType = payload.data.documentType;
                } else if (payload.from === 'production-order-receipts' || payload.from === 'production-order-issues') {
                    transfer.documentType = 'production-transfers';
                } else if (
                    payload.from === 'service-work-order-outgoing' ||
                    payload.from === 'service-work-order-incoming'
                ) {
                    transfer.documentType = 'service-transfers';
                }

                // Operation type and locations.
                const operationType = await app.collection('inventory.operation-types').findOne({
                    type: warehouseOperationType,
                    warehouseId: item.warehouseId,
                    $select: ['_id', 'numberingId', 'defaultSourceLocationId', 'defaultDestinationLocationId']
                });
                if (_.isObject(operationType)) {
                    // Define operation type.
                    transfer.operationTypeId = operationType._id;
                    transfer.numberingId = operationType.numberingId;

                    // Location.
                    if (
                        payload.from === 'sales-order' ||
                        payload.from === 'customer-invoice' ||
                        payload.from === 'income'
                    ) {
                        transfer.sourceLocationId = operationType.defaultSourceLocationId;
                        transfer.destinationLocationId = customerLocationId;
                        transfer.journalDescription = this.translate('Inventory - Cost of goods sold');
                    } else if (payload.from === 'sales-return' || payload.from === 'customer-return-invoice') {
                        transfer.sourceLocationId = customerLocationId;
                        transfer.destinationLocationId = operationType.defaultDestinationLocationId;
                        transfer.journalDescription = this.translate('Inventory - Sales returns');
                    } else if (
                        payload.from === 'purchase-order' ||
                        payload.from === 'vendor-invoice' ||
                        payload.from === 'expense'
                    ) {
                        transfer.sourceLocationId = vendorLocationId;
                        transfer.destinationLocationId = operationType.defaultDestinationLocationId;
                        transfer.journalDescription = this.translate('Inventory - Purchase goods receipts');
                    } else if (payload.from === 'purchase-return' || payload.from === 'vendor-return-invoice') {
                        transfer.sourceLocationId = operationType.defaultSourceLocationId;
                        transfer.destinationLocationId = vendorLocationId;
                        transfer.journalDescription = this.translate('Inventory - Purchase returns');
                    } else if (payload.from === 'production-order-receipts') {
                        transfer.sourceLocationId = productionLocationId;
                        transfer.destinationLocationId = operationType.defaultDestinationLocationId;
                    } else if (payload.from === 'production-order-issues') {
                        transfer.sourceLocationId = operationType.defaultSourceLocationId;
                        transfer.destinationLocationId = productionLocationId;

                        if (!!payload.destinationLocationId) {
                            transfer.destinationLocationId = payload.data.destinationLocationId;
                        }
                    } else if (payload.from === 'transport-incoming') {
                        transfer.sourceLocationId = transitLocationId;
                        transfer.destinationLocationId = operationType.defaultDestinationLocationId;
                        transfer.journalDescription = this.translate('Inventory - Transports goods receipts');
                    } else if (payload.from === 'transport-outgoing') {
                        transfer.sourceLocationId = operationType.defaultSourceLocationId;
                        transfer.destinationLocationId = transitLocationId;
                        transfer.journalDescription = this.translate('Inventory - Transports deliveries');
                    } else if (payload.from === 'partial-transfer') {
                        transfer.sourceLocationId = payload.data.sourceLocationId;
                        transfer.destinationLocationId = payload.data.destinationLocationId;
                        transfer.journalDescription = payload.data.journalDescription;
                    } else if (payload.from === 'service-work-order-outgoing') {
                        transfer.sourceLocationId = operationType.defaultSourceLocationId;
                        transfer.destinationLocationId = customerLocationId;
                    } else if (payload.from === 'service-work-order-incoming') {
                        transfer.sourceLocationId = customerLocationId;
                        transfer.destinationLocationId = operationType.defaultDestinationLocationId;
                    } else {
                        throw new app.errors.Unprocessable(this.translate('Cannot find source location!'));
                    }
                } else {
                    throw new app.errors.Unprocessable(this.translate('Cannot find operation type!'));
                }
            } else {
                // Get existing transfer.
                transfer = transfers[existingIndex];
            }

            // Prepare row.
            const row = {
                id: Random.id(8),
                productId: item.productId,
                productCode: product.code,
                productDefinition: product.definition,
                description: product.displayName,
                barcode: item.barcode,
                financialProjectId: item.financialProjectId,
                deliveryNote: item.deliveryNote,
                unitId: item.unitId,
                unitPrice: item.unitPrice,
                discount: 0,
                unitPriceAfterDiscount: item.unitPrice,
                freight: 0,
                cost: 0,
                total: 0,
                requestedQty: item.quantity,
                actualQty: 0
            };

            if (_.isString(item.description) && !_.isEmpty(item.description)) {
                row.description = item.description;
            }

            if (app.hasModule('pcm')) {
                row.pcmModelId = item.pcmModelId;
                row.pcmConfigurationId = item.pcmConfigurationId;
                row.pcmHash = item.pcmHash;
            }

            if (_.isPlainObject(r)) {
                row.stockQuantity = r.stockQuantity;
                row.orderedQuantity = r.orderedQuantity;
                row.assignedQuantity = r.assignedQuantity;
                row.availableQuantity = r.availableQuantity;
            } else {
                row.stockQuantity = 0;
                row.orderedQuantity = 0;
                row.assignedQuantity = 0;
                row.availableQuantity = 0;
            }

            // Auto add sub items when storage location
            // is turned off and product tacking is none!
            if (
                !app.setting('inventory.storageLocations') &&
                product.tracking !== 'serial' &&
                product.tracking !== 'lot' &&
                (warehouseOperationType === 'incoming' || warehouseOperationType === 'outgoing') &&
                !app.setting('inventory.useBulkEntryInTransfers')
            ) {
                row.actualQty = row.requestedQty;
                row.subItems = [
                    {
                        unitId: row.unitId,
                        sourceLocationId: transfer.sourceLocationId,
                        destinationLocationId: transfer.destinationLocationId,
                        quantity: row.actualQty
                    }
                ];
            }

            // Put-away strategy.
            if (
                !!app.setting('inventory.storageLocations') &&
                warehouseOperationType === 'incoming' &&
                (product.putawayStrategy === 'first-location' ||
                    product.putawayStrategy === 'last-location' ||
                    product.putawayStrategy === 'default-location') &&
                !app.setting('inventory.useBulkEntryInTransfers')
            ) {
                let destinationLocationId = null;

                if (product.putawayStrategy === 'first-location') {
                    const firstMove = await app.collection('inventory.moves').findOne({
                        productId: product._id,
                        warehouseId: warehouse._id,
                        type: 'incoming',
                        $sort: {date: 1},
                        $select: ['destinationLocationId']
                    });

                    if (!!firstMove) {
                        destinationLocationId = firstMove.destinationLocationId;
                    }
                } else if (product.putawayStrategy === 'last-location') {
                    const lastMove = await app.collection('inventory.moves').findOne({
                        productId: product._id,
                        warehouseId: warehouse._id,
                        type: 'incoming',
                        $sort: {systemDate: -1},
                        $select: ['destinationLocationId']
                    });

                    if (!!lastMove) {
                        destinationLocationId = lastMove.destinationLocationId;
                    }
                } else if (product.putawayStrategy === 'default-location') {
                    const w = (product.warehouseManagement ?? []).find(w => w.warehouseId === warehouse._id);

                    if (!!w && !!w.defaultPutawayLocationId && !w.isBlocked) {
                        destinationLocationId = w.defaultPutawayLocationId;
                    }
                }

                if (!!destinationLocationId) {
                    if (product.tracking === 'serial') {
                        const ratio = (product.unitRatios || {})[row.unitId] || 1;

                        row.actualQty = 0;
                        row.subItems = [];

                        for (let i = 0; i < Math.floor(ratio * row.requestedQty); i++) {
                            row.subItems.push({
                                unitId: product.baseUnitId,
                                serialNumber: '',
                                lotNumber: '',
                                sourceLocationId: transfer.sourceLocationId,
                                destinationLocationId: destinationLocationId,
                                quantity: 1
                            });
                        }
                    } else {
                        const ratio = (product.unitRatios || {})[row.unitId] || 1;
                        let qty = ratio * row.requestedQty;

                        row.actualQty = 0;
                        row.subItems = [];

                        const subItem = {
                            unitId: product.baseUnitId,
                            lotNumber: '',
                            sourceLocationId: transfer.sourceLocationId,
                            destinationLocationId: destinationLocationId,
                            quantity: qty
                        };

                        if (_.isPlainObject(r)) {
                            subItem.stockQuantity = r.stockQuantity;
                            subItem.orderedQuantity = r.orderedQuantity + qty;
                            subItem.assignedQuantity = r.assignedQuantity;
                            subItem.availableQuantity = r.availableQuantity + qty;
                        } else {
                            subItem.stockQuantity = 0;
                            subItem.orderedQuantity = qty;
                            subItem.assignedQuantity = 0;
                            subItem.availableQuantity = qty;
                        }

                        if (product.tracking !== 'lot') {
                            row.actualQty = row.requestedQty;
                        }

                        row.subItems.push(subItem);
                    }
                }
            }

            // Removal strategy.
            if (
                !!app.setting('inventory.storageLocations') &&
                warehouseOperationType === 'outgoing' &&
                (product.removalStrategy === 'fifo' ||
                    product.removalStrategy === 'lifo' ||
                    product.removalStrategy === 'fefo') &&
                !app.setting('inventory.useBulkEntryInTransfers')
            ) {
                // Get qty.
                const ratio = (product.unitRatios || {})[row.unitId] || 1;
                let requiredQty = ratio * row.requestedQty;

                // Get quantities query.
                const quantitiesQuery = {
                    productId: product._id,
                    warehouseId: warehouse._id,
                    'location.type': 'internal',
                    qty: {$gt: 0}
                };
                if (product.tracking === 'serial') {
                    quantitiesQuery.$limit = requiredQty;
                }
                if (product.removalStrategy === 'fifo') {
                    quantitiesQuery.$sort = {date: 1};
                } else if (product.removalStrategy === 'lifo') {
                    quantitiesQuery.$sort = {date: -1};
                } else if (product.removalStrategy === 'fefo' && product.tracking === 'serial') {
                    quantitiesQuery.$sort = {expirationDate: 1, date: 1};
                }

                // Get quantities.
                const quantities = await app.collection('inventory.quantities').find(quantitiesQuery);

                // Prepare sub items.
                let currentQty = 0;
                row.subItems = [];
                for (const quantity of quantities) {
                    if (product.tracking === 'serial') {
                        const subItem = {
                            unitId: product.baseUnitId,
                            serialNumber: quantity.serialNumber,
                            lotNumber: quantity.lotNumber,
                            sourceLocationId: quantity.locationId,
                            destinationLocationId: transfer.destinationLocationId,
                            quantity: 1
                        };

                        requiredQty -= subItem.quantity;

                        if (requiredQty < 0) {
                            break;
                        }

                        currentQty += subItem.quantity;

                        row.subItems.push(subItem);
                    } else {
                        const subItem = {
                            unitId: product.baseUnitId,
                            serialNumber: '',
                            lotNumber: quantity.lotNumber,
                            sourceLocationId: quantity.locationId,
                            destinationLocationId: transfer.destinationLocationId,
                            quantity: 0
                        };

                        if (quantity.qty >= requiredQty) {
                            subItem.quantity = requiredQty;
                        } else {
                            subItem.quantity = quantity.qty;
                        }

                        requiredQty -= subItem.quantity;

                        if (requiredQty < 0) {
                            break;
                        }
                        if (subItem.quantity === 0) {
                            break;
                        }

                        currentQty += subItem.quantity;

                        if (_.isPlainObject(r)) {
                            subItem.stockQuantity = r.stockQuantity;
                            subItem.orderedQuantity = r.orderedQuantity;
                            subItem.assignedQuantity = r.assignedQuantity + currentQty;
                            subItem.availableQuantity = r.availableQuantity - currentQty;
                        } else {
                            subItem.stockQuantity = 0;
                            subItem.orderedQuantity = 0;
                            subItem.assignedQuantity = currentQty;
                            subItem.availableQuantity = -currentQty;
                        }

                        row.subItems.push(subItem);
                    }
                }

                row.actualQty = currentQty / ratio;

                if (!!app.setting('inventory.useBulkEntryInTransfers')) {
                    row.actualQty = 0;
                    row.subItems = [];
                }
            }
            /*
            if (
                !!app.setting('inventory.storageLocations') &&
                warehouseOperationType === 'outgoing' &&
                (product.removalStrategy === 'fifo' ||
                    product.removalStrategy === 'lifo' ||
                    product.removalStrategy === 'fefo')
            ) {
                // Get qty.
                const ratio = (product.unitRatios || {})[row.unitId] || 1;
                const requiredQty = ratio * row.requestedQty;

                // Get quantities query.
                const quantitiesQuery = {
                    productId: product._id,
                    warehouseId: warehouse._id,
                    'location.type': 'internal'
                };
                if (product.tracking === 'serial') {
                    quantitiesQuery.$limit = requiredQty;
                }
                if (product.removalStrategy === 'fifo') {
                    quantitiesQuery.$sort = {date: 1};
                } else if (product.removalStrategy === 'lifo') {
                    quantitiesQuery.$sort = {date: -1};
                } else if (product.removalStrategy === 'fefo' && product.tracking === 'serial') {
                    quantitiesQuery.$sort = {expirationDate: 1, date: 1};
                }

                // Get quantities.
                const quantities = await app.collection('inventory.quantities').find(quantitiesQuery);

                // Prepare sub items.
                let currentQty = 0;
                row.subItems = [];
                for (const quantity of quantities) {
                    if (product.tracking === 'serial') {
                        const subItem = {
                            unitId: product.baseUnitId,
                            serialNumber: quantity.serialNumber,
                            lotNumber: quantity.lotNumber,
                            sourceLocationId: quantity.locationId,
                            destinationLocationId: transfer.destinationLocationId,
                            quantity: 1
                        };

                        currentQty += subItem.quantity;

                        row.subItems.push(subItem);
                    } else {
                        const subItem = {
                            unitId: product.baseUnitId,
                            serialNumber: '',
                            lotNumber: quantity.lotNumber,
                            sourceLocationId: quantity.locationId,
                            destinationLocationId: transfer.destinationLocationId,
                            quantity: 0
                        };

                        if (quantity.qty >= requiredQty) {
                            subItem.quantity = requiredQty;
                        } else {
                            subItem.quantity = quantity.qty;
                        }

                        currentQty += subItem.quantity;

                        if (_.isPlainObject(r)) {
                            subItem.stockQuantity = r.stockQuantity;
                            subItem.orderedQuantity = r.orderedQuantity;
                            subItem.assignedQuantity = r.assignedQuantity + currentQty;
                            subItem.availableQuantity = r.availableQuantity - currentQty;
                        } else {
                            subItem.stockQuantity = 0;
                            subItem.orderedQuantity = 0;
                            subItem.assignedQuantity = currentQty;
                            subItem.availableQuantity = -currentQty;
                        }

                        let breakLoop = false;
                        if (currentQty > requiredQty) {
                            const diff = currentQty - requiredQty;

                            subItem.quantity -= diff;

                            breakLoop = true;
                        }

                        row.subItems.push(subItem);

                        if (breakLoop) {
                            break;
                        }
                    }

                    if (currentQty >= requiredQty) {
                        break;
                    }
                }

                // Get actual qty.
                row.actualQty = currentQty / ratio;
            }*/

            // Round numbers.
            row.requestedQty = app.round(row.requestedQty || 0, 'unit');
            row.actualQty = app.round(row.actualQty || 0, 'unit');

            // Freight
            if (_.isFinite(item.freight)) {
                row.freight = item.freight;
            }

            // Cost
            row.cost = row.unitPriceAfterDiscount * row.actualQty + row.freight;
            row.total = row.unitPriceAfterDiscount * row.actualQty;

            // Add row to transfer.
            transfer.items.push(row);

            // Add transfer to transfer list if no exist!
            if (existingIndex === -1) {
                transfers.push(transfer);
            }
        }

        // Define transfer codes.
        for (const transfer of transfers) {
            transfer.code = await app.rpc('kernel.common.request-number', {
                numberingId: transfer.numberingId,
                save: true
            });
        }

        // Calculate transfer totals.
        for (const transfer of transfers) {
            let discount = 0;
            let discountAmount = 0;
            let subTotal = 0;
            let subTotalAfterDiscount = 0;
            let grandTotal = 0;

            if (Array.isArray(transfer.items)) {
                for (const row of transfer.items) {
                    subTotal += app.round((row.unitPrice || 0) * row.actualQty, 'total');
                    discountAmount += app.round(
                        (((row.unitPrice || 0) * (row.discount || 0)) / 100) * row.actualQty,
                        'total'
                    );
                }
            }
            discount = subTotal > 0 ? (discountAmount / subTotal) * 100 : 0;
            subTotalAfterDiscount = app.round(subTotal - discountAmount, 'total');
            grandTotal = app.round(subTotalAfterDiscount, 'total');

            transfer.subTotal = app.round(subTotal, 'total');
            transfer.discountAmount = app.round(discountAmount, 'total');
            transfer.subTotalAfterDiscount = subTotalAfterDiscount;
            transfer.grandTotal = app.round(grandTotal, 'total');
        }

        // Ready status.
        for (const transfer of transfers) {
            if (
                !transfer.connectedTransferId &&
                (transfer.status === 'draft' || transfer.status === 'waiting') &&
                Array.isArray(transfer.items) &&
                transfer.items.length > 0 &&
                !transfer.items.some(item => item.actualQty !== item.requestedQty)
            ) {
                transfer.status = 'ready';
            }

            if (payload.data.deliveryNote) {
                transfer.shipmentNote = payload.data.deliveryNote;
            }
        }

        // Create transfers.
        if (transfers.length > 0) {
            const createdTransfer = await app.collection('inventory.transfers').create(
                transfers.map(transfer => _.omit(transfer, ['warehouseId', 'numberingId'])),
                {user: params.user}
            );

            // noinspection ES6MissingAwait
            (async () => {
                try {
                    const transfers = Array.isArray(createdTransfer) ? createdTransfer : [createdTransfer];
                    const operationTypes = await app.collection('inventory.operation-types').find({
                        _id: {$in: _.uniq(transfers.map(transfer => transfer.operationTypeId))},
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    for (const transfer of transfers) {
                        const operationType = operationTypes.find(type => type._id === transfer.operationTypeId);

                        if (transfer.type === 'outgoing') {
                            const payload = {};
                            payload.documentId = transfer._id;
                            payload.documentCode = transfer.code;
                            payload.total = transfer.grandTotal ?? 0;
                            payload.items = (transfer.items || []).map(item => ({
                                id: item.productId,
                                code: item.productCode,
                                name: item.productDefinition,
                                quantity: item.requestedQty,
                                unitPrice: item.unitPrice ?? 0
                            }));
                            payload.deliveryAddress = transfer.deliveryAddress;
                            payload.scheduledDate = transfer.scheduledDate;
                            payload.currencyId = transfer.currencyId;
                            payload.partnerId = transfer.partnerId;
                            payload.warehouseId = (operationType || {}).warehouseId;
                            payload.orderType = 'sale-order';
                            payload.isMarketplaceOrder = false;
                            payload.marketplaceOrderId = null;

                            const order = await app.collection('sale.orders').findOne({
                                _id: transfer.referenceId,
                                $select: ['code', 'integrationPayload', 'storeId'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });
                            if (
                                _.isPlainObject(order) &&
                                _.isPlainObject(order.integrationPayload) &&
                                !_.isEmpty(order.integrationPayload)
                            ) {
                                payload.isMarketplaceOrder = true;
                                payload.marketplaceOrderId = order.code.split('/')?.[1] ?? null;
                                payload.storeId = order.storeId;
                            }

                            await app.rpc('inventory.create-integration-order', payload);
                        }

                        if (transfer.type === 'incoming') {
                            const integrationWarehouse = await app
                                .collection('inventory.integration-warehouses')
                                .findOne({erpWarehouseId: (operationType || {}).warehouseId});

                            if (integrationWarehouse) {
                                await app.collection('inventory.transfers').bulkWrite([
                                    {
                                        updateOne: {
                                            filter: {_id: transfer._id},
                                            update: {
                                                $set: {
                                                    'integrationPayload.orderType': 'purchase-order',
                                                    'integrationPayload.isIntegrationTransfer': true
                                                }
                                            }
                                        }
                                    }
                                ]);
                            }
                        }
                    }
                } catch (error) {
                    console.error('Fulfillment integration order error ->', error.message);
                }
            })();

            if (!!app.setting('inventory.useBulkEntryInTransfers')) {
                // noinspection ES6MissingAwait
                (async () => {
                    try {
                        const transfers = Array.isArray(createdTransfer) ? createdTransfer : [createdTransfer];
                        const operationTypes = await app.collection('inventory.operation-types').find({
                            _id: {$in: _.uniq(transfers.map(transfer => transfer.operationTypeId))},
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        const transfersOperations = [];
                        const subItemsOperations = [];

                        for (const transfer of transfers) {
                            const operationType = operationTypes.find(type => type._id === transfer.operationTypeId);
                            const warehouse = warehousesMap[operationType.warehouseId];

                            for (const item of transfer.items ?? []) {
                                const product = productsMap[item.productId];
                                const baseUnit = await app.collection('kernel.units').findOne({
                                    _id: product.baseUnitId,
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                // Put-away strategy.
                                if (
                                    !!app.setting('inventory.storageLocations') &&
                                    warehouseOperationType === 'incoming' &&
                                    (product.putawayStrategy === 'first-location' ||
                                        product.putawayStrategy === 'last-location' ||
                                        product.putawayStrategy === 'default-location') &&
                                    product.tracking !== 'serial' &&
                                    product.tracking !== 'lot'
                                ) {
                                    let destinationLocationId = null;

                                    if (product.putawayStrategy === 'first-location') {
                                        const firstMove = await app.collection('inventory.moves').findOne({
                                            productId: product._id,
                                            warehouseId: warehouse._id,
                                            type: 'incoming',
                                            $sort: {date: 1},
                                            $select: ['destinationLocationId']
                                        });

                                        if (!!firstMove) {
                                            destinationLocationId = firstMove.destinationLocationId;
                                        }
                                    } else if (product.putawayStrategy === 'last-location') {
                                        const lastMove = await app.collection('inventory.moves').findOne({
                                            productId: product._id,
                                            warehouseId: warehouse._id,
                                            type: 'incoming',
                                            $sort: {systemDate: -1},
                                            $select: ['destinationLocationId']
                                        });

                                        if (!!lastMove) {
                                            destinationLocationId = lastMove.destinationLocationId;
                                        }
                                    } else if (product.putawayStrategy === 'default-location') {
                                        const w = (product.warehouseManagement ?? []).find(
                                            w => w.warehouseId === warehouse._id
                                        );

                                        if (!!w && !!w.defaultPutawayLocationId && !w.isBlocked) {
                                            destinationLocationId = w.defaultPutawayLocationId;
                                        }
                                    }

                                    if (!!destinationLocationId) {
                                        const sourceLocation = await app.collection('inventory.locations').findOne({
                                            _id: transfer.sourceLocationId,
                                            $disableActiveCheck: true,
                                            $disableSoftDelete: true
                                        });
                                        const destinationLocation = await app
                                            .collection('inventory.locations')
                                            .findOne({
                                                _id: destinationLocationId,
                                                $disableActiveCheck: true,
                                                $disableSoftDelete: true
                                            });
                                        const ratio = (product.unitRatios || {})[item.unitId] || 1;
                                        let qty = ratio * item.requestedQty;

                                        const i = {};
                                        i.status = 'draft';
                                        i.id = Random.id(16);
                                        i.transferId = transfer._id;
                                        i.itemId = item.id;
                                        i.isValidated = false;
                                        i.productId = item.productId;
                                        i.productCode = item.productCode;
                                        i.productDefinition = item.productDefinition;
                                        i.barcode = item.barcode;
                                        i.sourceLocationId = sourceLocation._id;
                                        i.sourceLocationPath = sourceLocation.path;
                                        i.destinationLocationId = destinationLocation._id;
                                        i.destinationLocationPath = destinationLocation.path;
                                        i.acceptanceDate = app.datetime.local().toJSDate();
                                        i.productionDate = null;
                                        i.expirationDate = null;
                                        i.manufacturerWarrantyStartDate = null;
                                        i.manufacturerWarrantyEndDate = null;
                                        i.unitId = product.baseUnitId;
                                        i.unitName = baseUnit.name;
                                        i.qty = qty;

                                        subItemsOperations.push({
                                            insertOne: {document: i}
                                        });

                                        item.actualQty = item.requestedQty;
                                    }
                                }

                                // Removal strategy.
                                if (
                                    !!app.setting('inventory.storageLocations') &&
                                    warehouseOperationType === 'outgoing' &&
                                    (product.removalStrategy === 'fifo' ||
                                        product.removalStrategy === 'lifo' ||
                                        product.removalStrategy === 'fefo')
                                ) {
                                    const destinationLocation = await app.collection('inventory.locations').findOne({
                                        _id: transfer.destinationLocationId,
                                        $disableActiveCheck: true,
                                        $disableSoftDelete: true
                                    });

                                    // Get qty.
                                    const ratio = (product.unitRatios || {})[item.unitId] || 1;
                                    let requiredQty = ratio * item.requestedQty;

                                    // Get quantities query.
                                    const quantitiesQuery = {
                                        productId: product._id,
                                        warehouseId: warehouse._id,
                                        'location.type': 'internal',
                                        qty: {$gt: 0}
                                    };
                                    if (product.tracking === 'serial') {
                                        quantitiesQuery.$limit = requiredQty;
                                    }
                                    if (product.removalStrategy === 'fifo') {
                                        quantitiesQuery.$sort = {date: 1};
                                    } else if (product.removalStrategy === 'lifo') {
                                        quantitiesQuery.$sort = {date: -1};
                                    } else if (product.removalStrategy === 'fefo' && product.tracking === 'serial') {
                                        quantitiesQuery.$sort = {expirationDate: 1, date: 1};
                                    }

                                    // Get quantities.
                                    const quantities = await app
                                        .collection('inventory.quantities')
                                        .find(quantitiesQuery);

                                    // Prepare sub items.
                                    let currentQty = 0;
                                    for (const quantity of quantities) {
                                        const sourceLocation = await app.collection('inventory.locations').findOne({
                                            _id: quantity.locationId,
                                            $disableActiveCheck: true,
                                            $disableSoftDelete: true
                                        });

                                        if (product.tracking === 'serial') {
                                            const serialNumber = await app
                                                .collection('inventory.serial-numbers')
                                                .findOne({
                                                    serialNumber: quantity.serialNumber
                                                });
                                            const existingItemCount = await app
                                                .collection('inventory.transfer-sub-items')
                                                .count({
                                                    status: 'draft',
                                                    serialNumber: serialNumber.serialNumber,
                                                    sourceLocationId: sourceLocation._id
                                                });
                                            if (existingItemCount > 0) {
                                                continue;
                                            }

                                            const i = {};
                                            i.status = 'draft';
                                            i.id = Random.id(16);
                                            i.transferId = transfer._id;
                                            i.itemId = item.id;
                                            i.isValidated = false;
                                            i.productId = item.productId;
                                            i.productCode = item.productCode;
                                            i.productDefinition = item.productDefinition;
                                            i.barcode = item.barcode;
                                            i.sourceLocationId = sourceLocation._id;
                                            i.sourceLocationPath = sourceLocation.path;
                                            i.destinationLocationId = destinationLocation._id;
                                            i.destinationLocationPath = destinationLocation.path;
                                            i.serialNumber = serialNumber.serialNumber;
                                            i.lotNumber = serialNumber.lotNumber;
                                            i.acceptanceDate = serialNumber.acceptanceDate;
                                            i.productionDate = serialNumber.productionDate;
                                            i.expirationDate = serialNumber.expirationDate;
                                            i.manufacturerWarrantyStartDate =
                                                serialNumber.manufacturerWarrantyStartDate;
                                            i.manufacturerWarrantyEndDate = serialNumber.manufacturerWarrantyEndDate;
                                            i.unitId = product.baseUnitId;
                                            i.unitName = baseUnit.name;
                                            i.qty = 1;

                                            requiredQty -= i.qty;

                                            if (requiredQty < 0) {
                                                break;
                                            }

                                            currentQty += i.qty;

                                            subItemsOperations.push({
                                                insertOne: {document: i}
                                            });
                                        } else if (product.tracking === 'lot') {
                                            const lotNumber = await app.collection('inventory.lot-numbers').findOne({
                                                lotNumber: quantity.lotNumber
                                            });
                                            const existingItems = await app
                                                .collection('inventory.transfer-sub-items')
                                                .find({
                                                    status: 'draft',
                                                    productId: item.productId,
                                                    lotNumber: lotNumber.lotNumber,
                                                    sourceLocationId: sourceLocation._id
                                                });
                                            let availableQuantity = quantity.qty ?? 0;

                                            for (const existingItem of existingItems) {
                                                availableQuantity -= existingItem.qty ?? 0;
                                            }
                                            availableQuantity = Math.max(0, availableQuantity);

                                            if (!(availableQuantity > 0)) {
                                                continue;
                                            }

                                            const i = {};
                                            i.status = 'draft';
                                            i.id = Random.id(16);
                                            i.transferId = transfer._id;
                                            i.itemId = item.id;
                                            i.isValidated = false;
                                            i.productId = item.productId;
                                            i.productCode = item.productCode;
                                            i.productDefinition = item.productDefinition;
                                            i.barcode = item.barcode;
                                            i.sourceLocationId = sourceLocation._id;
                                            i.sourceLocationPath = sourceLocation.path;
                                            i.destinationLocationId = destinationLocation._id;
                                            i.destinationLocationPath = destinationLocation.path;
                                            i.lotNumber = lotNumber.lotNumber;
                                            i.acceptanceDate = lotNumber.acceptanceDate;
                                            i.productionDate = lotNumber.productionDate;
                                            i.expirationDate = lotNumber.expirationDate;
                                            i.manufacturerWarrantyStartDate = lotNumber.manufacturerWarrantyStartDate;
                                            i.manufacturerWarrantyEndDate = lotNumber.manufacturerWarrantyEndDate;
                                            i.unitId = product.baseUnitId;
                                            i.unitName = baseUnit.name;
                                            i.qty = 0;

                                            if (availableQuantity >= requiredQty) {
                                                i.qty = requiredQty;
                                            } else {
                                                i.qty = availableQuantity;
                                            }

                                            requiredQty -= i.qty;

                                            if (requiredQty < 0) {
                                                break;
                                            }

                                            if (i.qty === 0) {
                                                break;
                                            }

                                            currentQty += i.qty;

                                            subItemsOperations.push({
                                                insertOne: {document: i}
                                            });
                                        } else {
                                            const existingItems = await app
                                                .collection('inventory.transfer-sub-items')
                                                .find({
                                                    status: 'draft',
                                                    productId: item.productId,
                                                    sourceLocationId: sourceLocation._id
                                                });
                                            let availableQuantity = quantity.qty ?? 0;

                                            for (const existingItem of existingItems) {
                                                availableQuantity -= existingItem.qty ?? 0;
                                            }
                                            availableQuantity = Math.max(0, availableQuantity);

                                            if (!(availableQuantity > 0) && !product.negativeStock) {
                                                continue;
                                            }

                                            const i = {};
                                            i.status = 'draft';
                                            i.id = Random.id(16);
                                            i.transferId = transfer._id;
                                            i.itemId = item.id;
                                            i.isValidated = false;
                                            i.productId = item.productId;
                                            i.productCode = item.productCode;
                                            i.productDefinition = item.productDefinition;
                                            i.barcode = item.barcode;
                                            i.sourceLocationId = sourceLocation._id;
                                            i.sourceLocationPath = sourceLocation.path;
                                            i.destinationLocationId = destinationLocation._id;
                                            i.destinationLocationPath = destinationLocation.path;
                                            i.acceptanceDate = null;
                                            i.productionDate = null;
                                            i.expirationDate = null;
                                            i.manufacturerWarrantyStartDate = null;
                                            i.manufacturerWarrantyEndDate = null;
                                            i.unitId = product.baseUnitId;
                                            i.unitName = baseUnit.name;
                                            i.qty = 0;

                                            if (availableQuantity >= requiredQty || !!product.negativeStock) {
                                                i.qty = requiredQty;
                                            } else {
                                                i.qty = availableQuantity;
                                            }

                                            requiredQty -= i.qty;

                                            if (requiredQty < 0 && !product.negativeStock) {
                                                break;
                                            }

                                            if (i.qty === 0) {
                                                break;
                                            }

                                            currentQty += i.qty;

                                            subItemsOperations.push({
                                                insertOne: {document: i}
                                            });
                                        }
                                    }

                                    item.actualQty = currentQty / ratio;
                                }
                            }

                            let discount = 0;
                            let discountAmount = 0;
                            let subTotal = 0;
                            let subTotalAfterDiscount = 0;
                            let grandTotal = 0;
                            if (Array.isArray(transfer.items)) {
                                for (const row of transfer.items) {
                                    subTotal += app.round((row.unitPrice || 0) * row.actualQty, 'total');
                                    discountAmount += app.round(
                                        (((row.unitPrice || 0) * (row.discount || 0)) / 100) * row.actualQty,
                                        'total'
                                    );
                                }
                            }
                            discount = subTotal > 0 ? (discountAmount / subTotal) * 100 : 0;
                            subTotalAfterDiscount = app.round(subTotal - discountAmount, 'total');
                            grandTotal = app.round(subTotalAfterDiscount, 'total');
                            transfer.subTotal = app.round(subTotal, 'total');
                            transfer.discountAmount = app.round(discountAmount, 'total');
                            transfer.subTotalAfterDiscount = subTotalAfterDiscount;
                            transfer.grandTotal = app.round(grandTotal, 'total');

                            transfersOperations.push({
                                updateOne: {
                                    filter: {_id: transfer._id},
                                    update: {
                                        $set: {
                                            items: transfer.items,
                                            subTotal: transfer.subTotal,
                                            discountAmount: transfer.discountAmount,
                                            subTotalAfterDiscount: transfer.subTotalAfterDiscount,
                                            grandTotal: transfer.grandTotal
                                        }
                                    }
                                }
                            });
                        }

                        if (subItemsOperations.length > 0) {
                            await app.collection('inventory.transfer-sub-items').bulkWrite(subItemsOperations);
                        }

                        if (transfersOperations.length > 0) {
                            await app.collection('inventory.transfers').bulkWrite(transfersOperations);
                        }
                    } catch (error) {
                        console.error('Bulk entry automatic strategy error ->', error.message);
                    }
                })();
            }

            setTimeout(async () => {
                try {
                    const transfers = Array.isArray(createdTransfer) ? createdTransfer : [createdTransfer];

                    for (const transfer of transfers) {
                        await app.rpc('inventory.transfers-sync-document-delivery-status', {
                            transferId: transfer._id
                        });
                    }
                } catch (error) {
                    console.error(error.message);
                }
            }, 3000);

            return createdTransfer;
        }
    }
};
